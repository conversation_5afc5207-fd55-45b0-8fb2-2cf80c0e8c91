const { composePlugins, withNx } = require('@nx/next');

const envFile = require('./public/env/index.json');
// RUNTIME_ENV set from k8s helm chart deployment, passed to the container and needed on the frontend for analytics (currently)

const RELEASE_VERSION = require('./package.json')?.version;
const env = { ...(envFile ?? {}), RELEASE_VERSION, RUNTIME_ENV: process.env.RUNTIME_ENV || 'development' };

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  assetPrefix: '/money-build',
  env,
  experimental: {
    webpackMemoryOptimizations: true,
  },
  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },
  transpilePackages: [
    '@ant-design',
    'antd',
    '@coralogix/browser',
    '@ant-design/icons',
    '@ant-design/icons-svg',
    '@ant-design/v5-patch-for-react-19',
    'rc-util',
    'rc-pagination',
    'rc-picker',
    'rc-picker/lib/generate/dayjs',
    'rc-picker/lib/generate',
    'rc-picker/lib',
    'rc-table',
    'rc-tree',
    'react-tweet',
    'rc-cascader',
    'rc-checkbox',
    'rc-collapse',
    'rc-component',
    '@rc-component',
    '@rc-component/util',
    'rc-component/util',
    'rc-dialog',
    'rc-drawer',
    'rc-dropdown',
    'rc-field-form',
    'rc-image',
    'rc-input',
    'rc-input-number',
    'rc-mentions',
    'rc-menu',
    'rc-motion',
    'rc-notification',
    'rc-pagination',
    'rc-picker',
    'rc-progress',
    'rc-rate',
    'rc-resize-observer',
    'rc-segmented',
    'rc-select',
    'rc-slider',
    'rc-steps',
    'rc-switch',
    'rc-table',
    'rc-tabs',
    'rc-textarea',
    'rc-tooltip',
    'rc-tree',
    'rc-tree-select',
    'rc-upload',
    'rc-util',
  ],

  webpack: config => {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find(rule => rule.test?.test?.('.svg'));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        resourceQuery: /url/,
        test: /\.svg$/i, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
        test: /\.svg$/i, // exclude if *.svg?url
        use: ['@svgr/webpack', 'file-loader'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);
