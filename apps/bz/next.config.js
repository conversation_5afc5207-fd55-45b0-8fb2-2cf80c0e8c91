const { composePlugins, withNx } = require('@nx/next');

// const webpack = require('webpack');
// const DuplicatePackageCheckerPlugin = require('duplicate-package-checker-webpack-plugin');
// const { workspaceLayout, workspaceRoot } = require('@nrwl/devkit');
// const withBundleAnalyzer = require('@next/bundle-analyzer')({
//   enabled: process.env.ANALYZE === 'true',
// });

// const path = require('path');
const envData = require('./public/env/index.json');
// RUNTIME_ENV set from k8s helm chart deployment, passed to the container and needed on the frontend for analytics (currently)

const RELEASE_VERSION = require('./package.json')?.version;
const { GitRevisionPlugin } = require('git-revision-webpack-plugin');
const gitRevisionPlugin = new GitRevisionPlugin();

const isDocker = require('./utils/isDocker');

const env = {
  ...(envData ?? {}),
  ...(process.env.NODE_ENV === 'development' && {
    RUNTIME_ENV: 'development',
  }),
  // The isDocker check is here to prevent gitRevisionPlugin.commithash() from being called and erroring
  GIT_COMMITHASH: process.env.CI_COMMIT_SHA || (isDocker() ? '' : gitRevisionPlugin.commithash()) || '',
  RELEASE_VERSION,
};
/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  compiler: {
    styledComponents: true,
  },
  compress: true,
  env,
  experimental: {
    optimizePackageImports: [
      '@benzinga/blocks',
      '@benzinga/money',
      '@benzinga/hooks',
      '@benzinga/themetron',
      '@benzinga/blocks-utils',
      '@benzinga/basic-news-manager',
      '@benzinga/core-ui',
      '@benzinga/advanced-news-manager',
      '@benzinga/scanner-manager',
      '@benzinga/trade-ideas',
      '@benzinga/news',
      '@benzinga/logos-ui',
      '@benzinga/translate',
      '@benzinga/navigation-ui',
      '@benzinga/session-context',
      '@benzinga/session',
      '@fortawesome/*',
      '@benzinga/themed-icons',
      'react-i18next',
      'i18next',
      '@benzinga/user-context',
      '@benzinga/tracking-manager',
      '@segment/analytics-next',
    ],
    webpackMemoryOptimizations: true,
    webpackBuildWorker: true,
    preloadEntriesOnStart: false,
  },
  // RULE: Last match WINS
  async headers() {
    // Benzinga-Control - Same as Benzinga TTL for legacy reasons, but we can probably remove
    // Benzinga-TTL - Sets the cache TTL for the returned object
    // Benzinga-stale_while_revalidate - Sets max_stale_while_revalidate, or how long the object should be served while stale
    // Benzinga-stale_if_error - If there are errors, how long can we serve the stale object

    return [
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=300',
          },
          {
            key: 'Benzinga-TTL',
            value: '600s',
          },
        ],
        // 10m Default
        source: '/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Briefs, 60s
        source: '/.next/data/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        source: '/content/:id([0-9]+)/:lastpath*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=0',
          },
          {
            key: 'Benzinga-TTL',
            value: '0s',
          },
        ],
        source: '/api/sheets/update',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=31536000',
          },
          {
            key: 'Benzinga-TTL',
            value: '31536000s',
          },
        ],
        source: '/api/sheets/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        // source: '^/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/*',
        source: '/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        // source: '^/:cat1([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/*',
        source: '/:cat1([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        source:
          '/:cat1([0-9a-zA-Z-_]+)/:cat2([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '86400s',
          },
        ],
        // Article Cache, 1d
        source: '/article/:path*{/}?',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '120s',
          },
        ],
        source: '/quote/:path*{/}?',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=300',
          },
          {
            key: 'Benzinga-TTL',
            value: '300s',
          },
        ],
        // Homepage, 5m
        source: '/',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=14400',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '28800s',
          },
        ],
        source: '/watch',
      },
      // PreMarket
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=0',
          },
          {
            key: 'Benzinga-TTL',
            value: '300s',
          },
        ],
        source: '/premarket',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=7200',
          },
          {
            key: 'Benzinga-TTL',
            value: '7200s',
          },
        ],
        source: '/premarket-prep',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=14400',
          },
          {
            key: 'Benzinga-TTL',
            value: '14400s',
          },
        ],
        source: '/api/premarket-prep',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=0',
          },
          {
            key: 'Benzinga-TTL',
            value: '0s',
          },
        ],
        source: '/api/articles/analytics/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=0',
          },
          {
            key: 'Benzinga-TTL',
            value: '0s',
          },
        ],
        source: '/api/articles/draft/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '60s',
          },
        ],
        // API Cache Defaults
        source: '/api/cache/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
        ],
        // News API, 60s
        source: '/api/news',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=14400',
          },
          {
            key: 'Benzinga-TTL',
            value: '14400s',
          },
        ],
        // Videos (YouTube Playlists) API, 14400s
        source: '/api/videos/youtube-playlists',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Briefs, 60s
        source: '/briefs',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Recent, 60s
        source: '/recent',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Exclusives, 60s
        source: '/exclusives',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Watch, 60s
        source: '/watch',
      },
    ];
  },

  images: {
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    dangerouslyAllowSVG: true,
    deviceSizes: [320, 414, 640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    domains: [
      'image-util.benzinga.com',
      'assets.coingecko.com',
      'logo-cdn.benzinga.com',
      'benzinga.com',
      'cdnwp-s3.benzinga.com',
      'cdn.benzinga.com',
      'www.benzinga.com',
    ],
  },

  nx: {
    // Set this to true if you would like to use SVGR
    // See: https://github.com/gregberge/svgr
    svgr: false,
  },

  // Development optimizations
  ...(process.env.NODE_ENV === 'development' && {
    onDemandEntries: {
      // Period (in ms) where the server will keep pages in the buffer
      maxInactiveAge: 25 * 1000,
      // Number of pages that should be kept simultaneously without being disposed
      pagesBufferLength: 2,
    },
  }),

  productionBrowserSourceMaps: false,

  // Disable source maps in development for faster builds
  ...(process.env.NODE_ENV === 'development' && {
    experimental: {
      ...nextConfig.experimental,
      serverSourceMaps: false,
    },
  }),

  async redirects() {
    return [
      {
        destination: '/fda-calendar/:slug*',
        permanent: true,
        source: '/fda-biotech/:slug*',
      },
      {
        destination: '/analyst-stock-ratings',
        permanent: true,
        source: '/calendars/analyst-ratings',
      },
      {
        destination: '/analyst-stock-ratings/:slug*',
        permanent: true,
        source: '/analyst-ratings/:slug(all|upgrades|downgrades|initiations)',
      },
      {
        destination: '/calendars/unusual-options-activity',
        permanent: true,
        source: '/calendars/options',
      },
      {
        destination: '/quote/:ticker/dividends',
        permanent: true,
        source: '/quote/:ticker/dividend',
      },
    ];
  },
  // RULE: First match wins. 🤷
  async rewrites() {
    return [
      {
        destination: '/article/pressreleases/:id',
        source: '/press-releases/([a-z]{0,2})?:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/pressreleases/:id',
        source: '/pressreleases/:year([0-2][0-9])/:month([0-1][0-9])/([a-z]{0,2})?:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/pressreleases/:id',
        source: '/pressreleases/([a-z]{0,2})?:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/secfilings/:id',
        source: '/secfilings/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source: '/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source: '/content/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source: '/:cat1([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source:
          '/:cat1([0-9a-zA-Z-_]+)/:cat2([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/article/:id',
        source:
          '/:cat1([0-9a-zA-Z-_]+)/:cat2([0-9a-zA-Z-_]+)/:cat3([0-9a-zA-Z-_]+)/:year([0-2][0-9])/:month([0-1][0-9])/:id([0-9]+)/:lastpath*',
      },
      {
        destination: '/recent',
        source: '/latest',
      },
      {
        destination: '/razreport',
        source: '/RazReport',
      },
      {
        destination: '/sponsored',
        source: '/partner-content',
      },
    ];
  },
  transpilePackages: [
    '@ant-design',
    'antd',
    '@coralogix/browser',
    '@ant-design/icons',
    '@ant-design/icons-svg',
    '@ant-design/v5-patch-for-react-19',
    'rc-util',
    'rc-pagination',
    'rc-picker',
    'rc-picker/lib/generate/dayjs',
    'rc-picker/lib/generate',
    'rc-picker/lib',
    'rc-table',
    'rc-tree',
    'react-tweet',
    'rc-cascader',
    'rc-checkbox',
    'rc-collapse',
    'rc-component',
    '@rc-component',
    '@rc-component/util',
    'rc-component/util',
    'rc-dialog',
    'rc-drawer',
    'rc-dropdown',
    'rc-field-form',
    'rc-image',
    'rc-input',
    'rc-input-number',
    'rc-mentions',
    'rc-menu',
    'rc-motion',
    'rc-notification',
    'rc-pagination',
    'rc-picker',
    'rc-progress',
    'rc-rate',
    'rc-resize-observer',
    'rc-segmented',
    'rc-select',
    'rc-slider',
    'rc-steps',
    'rc-switch',
    'rc-table',
    'rc-tabs',
    'rc-textarea',
    'rc-tooltip',
    'rc-tree',
    'rc-tree-select',
    'rc-upload',
    'rc-util',
  ],
  webpack: (config, { dev }) => {
    // Memory optimization for development builds
    if (dev) {
      // Optimize webpack cache for development
      if (config.cache) {
        config.cache = {
          type: 'memory',
          maxGenerations: 1,
        };
      }

      // Reduce memory usage in development
      config.optimization = {
        ...config.optimization,
        removeAvailableModules: false,
        removeEmptyChunks: false,
        splitChunks: false,
      };
    }

    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find(rule => rule.test?.test?.('.svg'));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        resourceQuery: /url/,
        test: /\.svg$/i, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
        test: /\.svg$/i, // exclude if *.svg?url
        use: ['@svgr/webpack', 'file-loader'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
  ...(process.env.ANALYZE === 'true'
    ? [
        require('@next/bundle-analyzer')({
          enabled: true,
        }),
      ]
    : []),
];

module.exports = composePlugins(...plugins)(nextConfig);
